"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from 'react'

// Types
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  country?: string
  avatar?: string
  role: 'trader' | 'admin'
  accountType: 'demo' | 'live' | 'funded'
  joinedAt: string
  lastLogin?: string
  isVerified: boolean
  kycStatus: 'pending' | 'approved' | 'rejected'
  tradingExperience: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  isMockAuth: boolean
  login: (email: string, password: string) => Promise<void>
  signup: (userData: SignupData) => Promise<void>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<void>
  refreshUser: () => Promise<void>
  getOrders: () => Promise<any[]>
}

interface SignupData {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  country?: string
  tradingExperience?: string
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock user data for development
const mockUser: User = {
  id: "user_123",
  email: "<EMAIL>",
  firstName: "John",
  lastName: "Doe",
  phone: "+****************",
  country: "United States",
  avatar: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/placeholder-user.jpg",
  role: "trader",
  accountType: "funded",
  joinedAt: "2024-01-15T10:30:00Z",
  lastLogin: "2024-01-21T14:30:00Z",
  isVerified: true,
  kycStatus: "approved",
  tradingExperience: "intermediate"
}

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for existing session on mount
  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true)
      
      // Check localStorage for auth token
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('user_data')
      
      console.log('Auth check - token:', token ? 'found' : 'not found')
      console.log('Auth check - userData:', userData ? 'found' : 'not found')
      
      if (token && userData) {
        // In a real app, you'd validate the token with your API
        const user = JSON.parse(userData)
        console.log('Setting user:', user)
        setUser(user)
      } else {
        console.log('No token or userData found')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      // Clear invalid data
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true)

      console.log('Attempting login with backend API...')

      // Make real API call to your login endpoint
      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`Login API failed: ${response.status} - ${errorText}`)
        throw new Error(`Login failed: ${response.status}`)
      }

      const data = await response.json()
      console.log('Login successful, received data:', {
        hasToken: !!(data.access_token || data.token),
        hasUser: !!data.user
      })

      // Store the real access token
      const token = data.access_token || data.token
      if (!token) {
        throw new Error('No access token received from login API')
      }

      localStorage.setItem('auth_token', token)
      localStorage.setItem('user_data', JSON.stringify(data.user || { ...mockUser, email }))

      setUser(data.user || { ...mockUser, email })
      console.log('Real authentication successful')

    } catch (error) {
      console.error('Login failed:', error)
      console.log('Falling back to mock authentication for development...')

      // Fallback to mock for development if API is not available
      if (email && password) {
        const token = 'mock_jwt_token_' + Date.now()
        const userData = { ...mockUser, email }

        localStorage.setItem('auth_token', token)
        localStorage.setItem('user_data', JSON.stringify(userData))

        setUser(userData)
        console.log('Mock authentication successful - using development mode')
      } else {
        throw error
      }
    } finally {
      setIsLoading(false)
    }
  }

  const signup = async (userData: SignupData) => {
    try {
      setIsLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Create new user
      const newUser: User = {
        id: 'user_' + Date.now(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        country: userData.country,
        role: 'trader',
        accountType: 'demo',
        joinedAt: new Date().toISOString(),
        isVerified: false,
        kycStatus: 'pending',
        tradingExperience: (userData.tradingExperience as any) || 'beginner'
      }
      
      const token = 'mock_jwt_token_' + Date.now()
      
      // Store in localStorage
      localStorage.setItem('auth_token', token)
      localStorage.setItem('user_data', JSON.stringify(newUser))
      
      setUser(newUser)
    } catch (error) {
      console.error('Signup failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    // Clear storage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    
    // Clear state
    setUser(null)
    
    // Redirect to home
    window.location.href = '/'
  }

  const updateProfile = async (data: Partial<User>) => {
    try {
      setIsLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (user) {
        const updatedUser = { ...user, ...data }
        
        // Update localStorage
        localStorage.setItem('user_data', JSON.stringify(updatedUser))
        
        setUser(updatedUser)
      }
    } catch (error) {
      console.error('Profile update failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    await checkAuthStatus()
  }

  const getOrders = async (): Promise<any[]> => {
    try {
      // Get the real access token from localStorage
      const token = localStorage.getItem('auth_token')

      console.log('Available localStorage keys:', Object.keys(localStorage))
      console.log('Token found:', token ? 'Yes' : 'No')
      console.log('Token value:', token)

      if (!token) {
        throw new Error('No authentication token found. Please login first.')
      }

      // Check if this is a mock token (development fallback)
      const isMockToken = token.startsWith('mock_jwt_token_')

      if (isMockToken) {
        console.log('Using mock token - returning mock order data for development')
        // Return mock order data for development
        const mockOrders = [
          {
            id: 'order_1',
            symbol: 'EURUSD',
            type: 'buy',
            amount: 0.1,
            openPrice: 1.0850,
            currentPrice: 1.0865,
            profit: 15.0,
            status: 'open',
            openTime: '2024-01-21T10:30:00Z'
          },
          {
            id: 'order_2',
            symbol: 'GBPUSD',
            type: 'sell',
            amount: 0.05,
            openPrice: 1.2750,
            currentPrice: 1.2735,
            profit: 7.5,
            status: 'open',
            openTime: '2024-01-21T11:15:00Z'
          },
          {
            id: 'order_3',
            symbol: 'USDJPY',
            type: 'buy',
            amount: 0.2,
            openPrice: 148.50,
            currentPrice: 148.75,
            profit: 25.0,
            status: 'closed',
            openTime: '2024-01-20T14:20:00Z',
            closeTime: '2024-01-21T09:45:00Z'
          }
        ]
        return mockOrders
      }

      // Use the real token with Bearer format
      const authHeader = `Bearer ${token}`
      console.log('Using real auth token for API call')

      const response = await fetch('https://xthronebackend-9dbecc0e3719.herokuapp.com/order/order_ids', {
        method: 'GET',
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.error('API Response Status:', response.status)
        const responseText = await response.text()
        console.error('API Response Text:', responseText)

        // If unauthorized, the token might be expired or invalid
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired or invalid')
          // Clear invalid token and redirect to login
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_data')
          setUser(null)
          throw new Error('Authentication failed. Please login again.')
        }

        throw new Error(`Failed to fetch orders: ${response.status} - ${responseText}`)
      }

      const orders = await response.json()
      console.log('Orders fetched successfully from API:', orders)
      return orders
    } catch (error) {
      console.error('Error fetching orders:', error)
      throw error
    }
  }

  const isAuthenticated = !!user

  // Check if current authentication is using mock token
  const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null
  const isMockAuth = !!(token && token.startsWith('mock_jwt_token_'))

  console.log('Auth context - user:', user, 'isAuthenticated:', isAuthenticated, 'isMockAuth:', isMockAuth)

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    isMockAuth,
    login,
    signup,
    logout,
    updateProfile,
    refreshUser,
    getOrders
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Export types for use in other components
export type { User, AuthContextType, SignupData }
